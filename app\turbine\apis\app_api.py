from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTStatelessUserAuthentication

from turbine.services import *
from base_drf import AdaptionViewSet


class AppViewSet(AdaptionViewSet):
    """应用视图集"""
    authentication_classes = [JWTStatelessUserAuthentication]
    permission_classes = [IsAuthenticated]

    queryset = App.objects.all()
    serializer_class = AppSerializer
    default_actions = [AdaptionViewSet.ACTION_ALL, AdaptionViewSet.ACTION_RETRIEVE]
