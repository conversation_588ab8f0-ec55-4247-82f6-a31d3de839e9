# 叶片属性问题脑图

## 概述
本文档展示了130个叶片属性字段与对应问题的映射关系，使用Mermaid脑图形式呈现。

## 完整脑图

```mermaid
mindmap
  root((叶片属性问题生成))
    基本信息
      PART_NAME[零件名称]
        零件名称是什么？
        PART_NAME是什么？
        叫什么名字？
        名称是？
        查找零件名称
      PART_NUMBER[零件图号]
        零件图号是什么？
        PART_NUMBER是多少？
        图号是7N25.23.02.21吗？
        查找零件图号
        图号编码是什么？
      BLD_TYPE[叶片类型]
        是动叶还是静叶？
        叶片类型是什么？
        BLD_TYPE是什么？
        属于哪种类型？
        查找叶片类型
    级别与旋向
      STAGE[叶片级号]
        叶片级号是多少？
        STAGE是什么？
        是第几级？
        查找叶片级号
        属于哪一级叶片？
      STUFE[叶片级号]
        叶片级号是多少？
        STUFE是什么？
        是第几级？
        查找STUFE参数
        叶片级号值是多少？
      ROT[旋向]
        是左旋还是右旋？
        旋向是什么？
        ROT是多少？
        旋转方向是？
        查找旋向
    数量参数
      BLADES[整级叶片只数]
        整级叶片有多少只？
        BLADES是多少？
        总共有几只？
        查找整级叶片只数
        叶片数量是多少？
      BLADES0[标准叶片只数]
        标准叶片有多少只？
        BLADES0是多少？
        标准叶片数量是？
        查找标准叶片只数
        标准型叶片有几只？
      BLADES1[加厚叶片只数]
        加厚叶片有多少只？
        BLADES1是多少？
        加厚叶片数量是？
        查找加厚叶片只数
        加厚型叶片有几只？
      BLADES2[中分面叶片只数]
        中分面叶片有多少只？
        BLADES2是多少？
        中分面叶片数量是？
        查找BLADES2参数
        中分面叶片只数值是多少？
    尺寸参数
      DA[外径]
        外径是多少？
        DA值是什么？
        查找外径尺寸
        外径参数是？
        外圆直径是多少？
      DI[内径]
        内径是多少？
        DI值是什么？
        查找内径尺寸
        内径参数是？
        内圆直径是多少？
      DB[最底部插值截面的直径]
        最底部插值截面直径是多少？
        DB是什么？
        查找DB参数
        底部截面直径是？
        最底部直径值是多少？
      DHS[静叶根部切口直径]
        静叶根部切口直径是多少？
        DHS是什么？
        查找DHS参数
        根部切口直径是？
        叶根切口直径值是多少？
      DI3[叶型出汽边底部截面A2直径]
        叶型出汽边底部截面A2直径是多少？
        DI3是什么？
        查找DI3参数
        A2截面直径是？
        出汽边底部A2截面直径值是多少？
      DA1[叶型出汽边底部截面A1直径]
        叶型出汽边底部截面A1直径是多少？
        DA1是什么？
        查找DA1参数
        A1截面直径是？
        出汽边底部A1截面直径值是多少？
    重量参数
      weight[重量]
        重量是多少？
        weight是什么？
        查找weight参数
        有多重？
        重量值是多少？
      NET_WEIGHT[净重量]
        净重量是多少？
        NET_WEIGHT是什么？
        查找NET_WEIGHT参数
        净重是多少？
        净重量值是多少？
      weight_rough[毛坯质量]
        毛坯质量是多少？
        weight_rough是什么？
        查找weight_rough参数
        毛坯重量是多少？
        毛坯质量值是多少？
      weight_fb[中分面叶片重量]
        中分面叶片重量是多少？
        weight_fb是什么？
        查找weight_fb参数
        中分面叶片重量是？
        中分面叶片重量值是多少？
    材料与工艺
      MATERIAL[材料名称]
        材料名称是什么？
        MATERIAL是什么？
        查找MATERIAL参数
        是什么材料制作的？
        材料是什么？
      HEAT_TREATMENT[热处理]
        热处理是什么？
        HEAT_TREATMENT是什么？
        查找HEAT_TREATMENT参数
        采用什么热处理？
        热处理工艺是什么？
      MATERIAL_REF[叶片采购规范]
        叶片采购规范是什么？
        MATERIAL_REF是什么？
        查找MATERIAL_REF参数
        采购规范是？
        叶片采购规范值是什么？
      TYPE_ROUGH[毛坯类型]
        毛坯类型是什么？
        TYPE_ROUGH是什么？
        查找TYPE_ROUGH参数
        毛坯是什么类型？
        毛坯类型值是什么？
    叶根参数
      TYPE[叶根类型]
        叶根类型是什么？
        TYPE是什么？
        查找TYPE参数
        叶根是什么类型？
        叶根类型值是什么？
      ROOT_TYPE[叶根类型]
        叶根类型是什么？
        ROOT_TYPE是什么？
        查找ROOT_TYPE参数
        叶根是什么类型？
        叶根类型值是什么？
      ROOT[叶根图号]
        叶根图号是什么？
        ROOT是什么？
        查找ROOT参数
        叶根图号是？
        叶根图号值是什么？
      ROOT_NO[叶根图号]
        叶根图号是什么？
        ROOT_NO是什么？
        查找ROOT_NO参数
        叶根图号是？
        叶根图号值是什么？
      V01R[叶根宽度]
        叶根宽度是多少？
        V01R是什么？
        查找V01R参数
        叶根宽度是？
        叶根宽度值是多少？
      ROOT_WIDTH[叶根宽度]
        叶根宽度是多少？
        ROOT_WIDTH是什么？
        查找ROOT_WIDTH参数
        叶根宽度是？
        叶根宽度值是多少？
      V02R[叶根颈部宽度]
        叶根颈部宽度是多少？
        V02R是什么？
        查找V02R参数
        叶根颈部宽度是？
        叶根颈部宽度值是多少？
    节距与角度
      DECKWI[围带节距角]
        围带节距角是多少？
        DECKWI是什么？
        查找围带节距角
        围带角度是？
        DECKWI参数值是多少？
      DPSDA[动叶围带顶部中间处节距]
        动叶围带顶部中间处节距是多少？
        DPSDA是什么？
        查找DPSDA参数
        围带顶部节距是？
        围带顶部中间节距值是多少？
      DPSDI[静叶围带底部中间处节距]
        静叶围带底部中间处节距是多少？
        DPSDI是什么？
        查找DPSDI参数
        围带底部节距是？
        围带底部中间节距值是多少？
      FUSSWI[叶根节距角]
        叶根节距角是多少？
        FUSSWI是什么？
        查找FUSSWI参数
        叶根节距角是？
        叶根节距角度值是多少？
    截面参数
      Ax_0[截面代号Ax_0]
        截面代号Ax_0是什么？
        Ax_0是什么？
        查找Ax_0参数
        截面代号Ax_0是？
        截面代号Ax_0值是什么？
      Ax_1[截面代号Ax_1]
        截面代号Ax_1是什么？
        Ax_1是什么？
        查找Ax_1参数
        截面代号Ax_1是？
        截面代号Ax_1值是什么？
      Ax_2[截面代号Ax_2]
        截面代号Ax_2是什么？
        Ax_2是什么？
        查找Ax_2参数
        截面代号Ax_2是？
        截面代号Ax_2值是什么？
      Ax_3[截面代号Ax_3]
        截面代号Ax_3是什么？
        Ax_3是什么？
        查找Ax_3参数
        截面代号Ax_3是？
        截面代号Ax_3值是什么？
    坐标参数
      XMIN_0[X坐标最小值XMIN_0]
        X坐标最小值XMIN_0是多少？
        XMIN_0是什么？
        查找XMIN_0参数
        X坐标最小值XMIN_0是？
        X坐标最小值XMIN_0值是多少？
      YMAX_0[Y坐标最大值YMAX_0]
        Y坐标最大值YMAX_0是多少？
        YMAX_0是什么？
        查找YMAX_0参数
        Y坐标最大值YMAX_0是？
        Y坐标最大值YMAX_0值是多少？
    安装角参数
      BetaBi_0[安装角BetaBi_0]
        安装角BetaBi_0是多少？
        BetaBi_0是什么？
        查找BetaBi_0参数
        安装角BetaBi_0是？
        安装角BetaBi_0值是多少？
      BetaBi_1[安装角BetaBi_1]
        安装角BetaBi_1是多少？
        BetaBi_1是什么？
        查找BetaBi_1参数
        安装角BetaBi_1是？
        安装角BetaBi_1值是多少？
```

## 问题统计

### 总体统计
- **字段总数**: 130个
- **问题总数**: 650个（每个字段5个问题）
- **问题类型**: 5种不同问法

### 问题类型分布
1. **直接询问**: "...是什么？"
2. **参数查询**: "...的[KEY]是什么？"
3. **查找指令**: "查找...的[参数]"
4. **描述性询问**: "这个叶片...的...是？"
5. **值获取**: "...值是多少？"

### 字段分类统计
- **基本信息类**: 3个字段（PART_NAME, PART_NUMBER, BLD_TYPE）
- **级别与旋向类**: 3个字段（STAGE, STUFE, ROT）
- **数量参数类**: 4个字段（BLADES, BLADES0, BLADES1, BLADES2）
- **尺寸参数类**: 20+个字段（各种直径、长度参数）
- **重量参数类**: 4个字段（weight, NET_WEIGHT, weight_rough, weight_fb）
- **材料与工艺类**: 4个字段（MATERIAL, HEAT_TREATMENT, MATERIAL_REF, TYPE_ROUGH）
- **叶根参数类**: 30+个字段（各种叶根相关参数）
- **节距与角度类**: 10+个字段（各种节距和角度参数）
- **截面参数类**: 16个字段（Ax_0到Ax_3, HX_0到HX_3等）
- **坐标参数类**: 8个字段（XMIN_0到XMIN_3, YMAX_0到YMAX_3）
- **其他专业参数**: 剩余字段

## 使用说明

1. **问题查询**: 可以根据字段名或中文释义快速定位相关问题
2. **问题变体**: 每个字段提供5种不同的问法，适应不同的查询习惯
3. **实际应用**: 所有问题都使用实际图号"7N25.23.02.21"，便于实际应用
4. **系统训练**: 可用于AI问答系统的训练数据
5. **测试验证**: 可用于叶片参数查询系统的功能测试

## 扩展建议

1. **问题丰富**: 可以为每个字段增加更多问法变体
2. **上下文问题**: 可以增加多字段组合的复合问题
3. **模糊查询**: 可以增加模糊匹配的问题形式
4. **专业术语**: 可以增加更多专业术语的问法
5. **多语言**: 可以扩展为多语言问题集合
