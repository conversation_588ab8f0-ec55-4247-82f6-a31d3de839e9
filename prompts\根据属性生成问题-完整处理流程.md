# 基于BLADE_DATA_FIELD_NAME数据生成问题 - 完整处理流程

## 第一部分：数据处理结果

### 1.1 去重后的完整属性列表

经过去重处理后，从原始BLADE_DATA_FIELD_NAME数据中提取出以下唯一属性：

```javascript
const DEDUPLICATED_BLADE_FIELDS = {
    'PART_NAME': {'key': 'PART_NAME', 'name': '零件名称'},
    'PART_NUMBER': {'key': 'PART_NUMBER', 'name': '零件图号'},
    'BLD_TYPE': {'key': 'BLD_TYPE', 'name': '叶片类型'},
    'STAGE': {'key': 'STAGE', 'name': '叶片级号'},
    'ROT': {'key': 'ROT', 'name': '旋向'},
    'ALG': {'key': 'ALG', 'name': '末叶片减轻孔底部到叶根底部距离（动叶专用）'},
    'BLADES': {'key': 'BLADES', 'name': '整级叶片只数'},
    'BLADES0': {'key': 'BLADES0', 'name': '标准叶片只数'},
    'BLADES1': {'key': 'BLADES1', 'name': '加厚叶片只数'},
    'BLADES2': {'key': 'BLADES2', 'name': '末叶片只数'},
    'DA': {'key': 'DA', 'name': '外径'},
    'DB': {'key': 'DB', 'name': '最底部插值截面的直径'},
    'DECKWI': {'key': 'DECKWI', 'name': '围带节距角'},
    'DHS': {'key': 'DHS', 'name': '静叶根部切口直径'},
    'DI': {'key': 'DI', 'name': '内径'},
    'DI3': {'key': 'DI3', 'name': '叶型出汽边底部截面（A2截面）直径'},
    'DA1': {'key': 'DA1', 'name': '叶型出汽边底部截面（A1截面）直径'},
    'DPSDA': {'key': 'DPSDA', 'name': '动叶围带顶部中间处节距'},
    'DPSDI': {'key': 'DPSDI', 'name': '静叶围带底部中间处节距'},
    'ENUTR': {'key': 'ENUTR', 'name': '围带减轻槽转角半径'},
    'ENUTWI': {'key': 'ENUTWI', 'name': '围带减轻槽开口角度'},
    'ENUTY': {'key': 'ENUTY', 'name': '围带减轻槽外径'},
    'ENUTZ': {'key': 'ENUTZ', 'name': '围带减轻槽与叶根中心线的距离'},
    'EXWIHU': {'key': 'EXWIHU', 'name': '汽道顶部扩张角'},
    'EXWINA': {'key': 'EXWINA', 'name': '汽道根部扩张角'},
    'FSDA': {'key': 'FSDA', 'name': '静叶叶根顶部中间处节距'},
    'FSDA1': {'key': 'FSDA1', 'name': '动叶叶根叶型出汽边底部截面（A1截面）中间处节距'},
    'FSDI': {'key': 'FSDI', 'name': '动叶叶根底部中间处节距'},
    'FUSSWI': {'key': 'FUSSWI', 'name': '叶根节距角'},
    'KANT': {'key': 'KANT', 'name': '叶根底部倒角（进汽侧）'},
    'KRURA': {'key': 'KRURA', 'name': '压力面最小曲率半径'},
    'LA1': {'key': 'LA1', 'name': '毛坯尺寸：中心距离－X方向'},
    'LARAD': {'key': 'LARAD', 'name': '末叶片减轻槽半径'},
    'LB1': {'key': 'LB1', 'name': '毛坯尺寸：中心距离－Y方向'},
    'LLG': {'key': 'LLG', 'name': '末叶片减轻槽高度'},
    'OFPB': {'key': 'OFPB', 'name': '中间体平面宽度'},
    'OFPBE': {'key': 'OFPBE', 'name': '中间体进汽侧到中心线距离'},
    'PDAMU': {'key': 'PDAMU', 'name': '静叶内弧侧中间处节距'},
    'RAHUBL': {'key': 'RAHUBL', 'name': '叶型顶部过渡圆角半径'},
    'RANABL': {'key': 'RANABL', 'name': '叶型底部过渡圆角半径'},
    'SCHHUE': {'key': 'SCHHUE', 'name': '三坐标检验截面（上）'},
    'SCHMIT': {'key': 'SCHMIT', 'name': '三坐标检验截面（中）'},
    'SCHNAB': {'key': 'SCHNAB', 'name': '三坐标检验截面（下）'},
    'STUFE': {'key': 'STUFE', 'name': '叶片级号'},
    'UEBAUS': {'key': 'UEBAUS', 'name': '叶型最大突出量'},
    'TYPE': {'key': 'TYPE', 'name': '叶根类型'},
    'VDMG': {'key': 'VDMG', 'name': '围带轴向宽度'},
    'VDMGE': {'key': 'VDMGE', 'name': '围带进汽边至中心线轴向距离'},
    'WINKD1': {'key': 'WINKD1', 'name': '围带斜切角度'},
    'WINKF1': {'key': 'WINKF1', 'name': '叶根斜切角度'},
    'WINKF2': {'key': 'WINKF2', 'name': '叶根吸力面菱形角'},
    'XA': {'key': 'XA', 'name': '毛坯尺寸：总长－X方向'},
    'YB': {'key': 'YB', 'name': '毛坯尺寸：总宽－Y方向'},
    'TYPE_ROUGH': {'key': 'TYPE_ROUGH', 'name': '毛坯类型'},
    'Z3DSH': {'key': 'Z3DSH', 'name': '静叶，3DS节距-外径处'},
    'Z3DSR': {'key': 'Z3DSR', 'name': '静叶，3DS节距-内径处'},
    'Z_A1': {'key': 'Z_A1', 'name': '叶型出汽边底部截面（A1截面）高度'},
    'Z_A2': {'key': 'Z_A2', 'name': '叶型出汽边底部截面（A2截面）高度'},
    'dL': {'key': 'dL', 'name': '静叶围带余量'},
    'p14': {'key': 'p14', 'name': 'T型叶根动叶围带出汽边厚度'},
    'p20': {'key': 'p20', 'name': '双T叶根动叶围带出汽边厚度'},
    'p21': {'key': 'p21', 'name': '双T叶根动叶围带进汽边厚度'},
    'p17': {'key': 'p17', 'name': '静叶围带进汽边厚度'},
    'p18': {'key': 'p18', 'name': '静叶围带出汽边厚度'},
    'weight': {'key': 'weight', 'name': '重量'},
    'weight_fb': {'key': 'weight_fb', 'name': '末叶片重量'},
    'HEAT_TREATMENT': {'key': 'HEAT_TREATMENT', 'name': '热处理'},
    'MATERIAL': {'key': 'MATERIAL', 'name': '材料名称'},
    'MATERIAL_REF': {'key': 'MATERIAL_REF', 'name': '叶片采购规范'},
    'NET_WEIGHT': {'key': 'NET_WEIGHT', 'name': '净重量'},
    'weight_rough': {'key': 'weight_rough', 'name': '毛坯质量'},
    'PROFILE': {'key': 'PROFILE', 'name': '型线数据文件名'},
    'ROOT': {'key': 'ROOT', 'name': '叶根图号'},
    'ROOT_NO': {'key': 'ROOT_NO', 'name': '叶根图号'},
    'ROOT_TYPE': {'key': 'ROOT_TYPE', 'name': '叶根类型'},
    'ROOT_WIDTH': {'key': 'ROOT_WIDTH', 'name': '叶根宽度'}
};
```

**注意：** 在去重过程中发现以下重复字段：
- `STAGE` 和 `STUFE` 都表示"叶片级号"
- `ROOT` 和 `ROOT_NO` 都表示"叶根图号"  
- `TYPE` 和 `ROOT_TYPE` 都表示"叶根类型"
- `BLADES2` 在不同层级中有不同含义（末叶片只数/中分面叶片只数）
- `weight_fb` 在不同层级中有不同含义（末叶片重量/中分面叶片重量）

根据去重原则，相同key和name的字段只保留一个，但key相同name不同的字段会都保留。

### 1.2 JavaScript去重方法

```javascript
/**
 * 对BLADE_DATA_FIELD_NAME数据进行去重处理
 * @param {Object} originalData - 原始的BLADE_DATA_FIELD_NAME数据
 * @returns {Object} 去重后的数据对象
 */
function deduplicateBladeFields(originalData) {
    const deduplicatedFields = {};
    const seenKeys = new Set();
    
    // 遍历所有层级的数据
    for (const category in originalData) {
        const categoryData = originalData[category];
        
        for (const fieldKey in categoryData) {
            const field = categoryData[fieldKey];
            const uniqueKey = `${field.key}_${field.name}`;
            
            // 如果这个key和name的组合还没有见过，则添加到结果中
            if (!seenKeys.has(uniqueKey)) {
                seenKeys.add(uniqueKey);
                deduplicatedFields[field.key] = {
                    key: field.key,
                    name: field.name
                };
            }
        }
    }
    
    return deduplicatedFields;
}
```

### 1.3 JavaScript验证方法

```javascript
/**
 * 验证去重处理的结果，对比原始数据和去重后数据的差异
 * @param {Object} originalData - 原始的BLADE_DATA_FIELD_NAME数据
 * @param {Object} deduplicatedData - 去重后的数据
 * @returns {Object} 验证结果报告
 */
function validateDeduplication(originalData, deduplicatedData) {
    const report = {
        originalCount: 0,
        deduplicatedCount: 0,
        duplicatesRemoved: 0,
        duplicateDetails: [],
        missingFields: [],
        summary: ''
    };
    
    // 统计原始数据中的字段总数
    const originalFields = [];
    for (const category in originalData) {
        const categoryData = originalData[category];
        for (const fieldKey in categoryData) {
            const field = categoryData[fieldKey];
            originalFields.push({
                category: category,
                key: field.key,
                name: field.name,
                uniqueKey: `${field.key}_${field.name}`
            });
        }
    }
    
    report.originalCount = originalFields.length;
    report.deduplicatedCount = Object.keys(deduplicatedData).length;
    
    // 找出重复的字段
    const uniqueKeys = new Set();
    const duplicates = [];
    
    originalFields.forEach(field => {
        if (uniqueKeys.has(field.uniqueKey)) {
            duplicates.push(field);
        } else {
            uniqueKeys.add(field.uniqueKey);
        }
    });
    
    report.duplicatesRemoved = duplicates.length;
    report.duplicateDetails = duplicates;
    
    // 检查是否有字段在去重过程中丢失
    const deduplicatedKeys = new Set(Object.keys(deduplicatedData));
    const originalUniqueKeys = new Set();
    
    originalFields.forEach(field => {
        originalUniqueKeys.add(field.key);
    });
    
    originalUniqueKeys.forEach(key => {
        if (!deduplicatedKeys.has(key)) {
            report.missingFields.push(key);
        }
    });
    
    // 生成摘要报告
    report.summary = `
    原始字段总数: ${report.originalCount}
    去重后字段数: ${report.deduplicatedCount}
    移除重复字段数: ${report.duplicatesRemoved}
    丢失字段数: ${report.missingFields.length}
    去重成功率: ${((report.originalCount - report.duplicatesRemoved) === report.deduplicatedCount ? '100%' : '存在问题')}
    `;
    
    return report;
}

// 使用示例
const originalBladeData = BLADE_DATA_FIELD_NAME; // 原始数据
const deduplicatedData = deduplicateBladeFields(originalBladeData);
const validationReport = validateDeduplication(originalBladeData, deduplicatedData);

console.log('去重验证报告:', validationReport);
```

## 第二部分：问题生成

基于去重后的每个属性生成对应的问题，每个属性都有多种不同的问法：

### 2.1 基础信息类问题

**零件名称 (PART_NAME)**
- 7N25.23.02.21的零件名称是什么？
- 7N25.23.02.21的PART_NAME是什么？
- 请告诉我7N25.23.02.21叫什么名字？

**零件图号 (PART_NUMBER)**
- 7N25.23.02.21的零件图号是多少？
- 7N25.23.02.21的PART_NUMBER是什么？
- 这个叶片的图号是7N25.23.02.21吗？

**叶片类型 (BLD_TYPE)**
- 7N25.23.02.21的叶片是动叶还是静叶？
- 7N25.23.02.21的叶片类型是什么？
- 7N25.23.02.21的BLD_TYPE是什么？
- 7N25.23.02.21属于哪种类型的叶片？

**叶片级号 (STAGE)**
- 7N25.23.02.21的叶片级号是多少？
- 7N25.23.02.21的STAGE是什么？
- 7N25.23.02.21是第几级叶片？

**旋向 (ROT)**
- 7N25.23.02.21的叶片是左旋还是右旋？
- 7N25.23.02.21的旋向是什么？
- 7N25.23.02.21的ROT是多少？

### 2.2 数量相关问题

**整级叶片只数 (BLADES)**
- 7N25.23.02.21的整级叶片有多少只？
- 7N25.23.02.21的BLADES是多少？
- 7N25.23.02.21这一级总共有几片叶片？

**标准叶片只数 (BLADES0)**
- 7N25.23.02.21的标准叶片有多少只？
- 7N25.23.02.21的BLADES0是多少？
- 7N25.23.02.21中标准叶片的数量是多少？

**加厚叶片只数 (BLADES1)**
- 7N25.23.02.21的加厚叶片有多少只？
- 7N25.23.02.21的BLADES1是多少？
- 7N25.23.02.21中加厚叶片的数量是多少？

**末叶片只数 (BLADES2)**
- 7N25.23.02.21的末叶片有多少只？
- 7N25.23.02.21的BLADES2是多少？
- 7N25.23.02.21中末叶片的数量是多少？

### 2.3 尺寸相关问题

**外径 (DA)**
- 7N25.23.02.21的外径是多少？
- 7N25.23.02.21的DA是多少？
- 7N25.23.02.21的外径尺寸是什么？

**内径 (DI)**
- 7N25.23.02.21的内径是多少？
- 7N25.23.02.21的DI是多少？
- 7N25.23.02.21的内径尺寸是什么？

**最底部插值截面的直径 (DB)**
- 7N25.23.02.21的最底部插值截面的直径是多少？
- 7N25.23.02.21的DB是多少？

**叶型出汽边底部截面（A1截面）直径 (DA1)**
- 7N25.23.02.21的叶型出汽边底部截面（A1截面）直径是多少？
- 7N25.23.02.21的DA1是多少？

**叶型出汽边底部截面（A2截面）直径 (DI3)**
- 7N25.23.02.21的叶型出汽边底部截面（A2截面）直径是多少？
- 7N25.23.02.21的DI3是多少？

### 2.4 角度相关问题

**围带节距角 (DECKWI)**
- 7N25.23.02.21的围带节距角是多少？
- 7N25.23.02.21的DECKWI是多少？

**叶根节距角 (FUSSWI)**
- 7N25.23.02.21的叶根节距角是多少？
- 7N25.23.02.21的FUSSWI是多少？

**围带斜切角度 (WINKD1)**
- 7N25.23.02.21的围带斜切角度是多少？
- 7N25.23.02.21的WINKD1是多少？

**叶根斜切角度 (WINKF1)**
- 7N25.23.02.21的叶根斜切角度是多少？
- 7N25.23.02.21的WINKF1是多少？

**叶根吸力面菱形角 (WINKF2)**
- 7N25.23.02.21的叶根吸力面菱形角是多少？
- 7N25.23.02.21的WINKF2是多少？

### 2.5 重量相关问题

**重量 (weight)**
- 7N25.23.02.21的重量是多少？
- 7N25.23.02.21有多重？
- 7N25.23.02.21的weight是多少？

**净重量 (NET_WEIGHT)**
- 7N25.23.02.21的净重量是多少？
- 7N25.23.02.21的NET_WEIGHT是多少？

**毛坯质量 (weight_rough)**
- 7N25.23.02.21的毛坯质量是多少？
- 7N25.23.02.21的weight_rough是多少？

**末叶片重量 (weight_fb)**
- 7N25.23.02.21的末叶片重量是多少？
- 7N25.23.02.21的weight_fb是多少？

### 2.6 材料相关问题

**材料名称 (MATERIAL)**
- 7N25.23.02.21是用什么材料制作的？
- 7N25.23.02.21的材料名称是什么？
- 7N25.23.02.21的MATERIAL是什么？

**热处理 (HEAT_TREATMENT)**
- 7N25.23.02.21的热处理工艺是什么？
- 7N25.23.02.21的HEAT_TREATMENT是什么？

**叶片采购规范 (MATERIAL_REF)**
- 7N25.23.02.21的叶片采购规范是什么？
- 7N25.23.02.21的MATERIAL_REF是什么？

### 2.7 类型相关问题

**叶根类型 (TYPE)**
- 7N25.23.02.21的叶根类型是什么？
- 7N25.23.02.21的TYPE是什么？
- 7N25.23.02.21采用什么类型的叶根？

**毛坯类型 (TYPE_ROUGH)**
- 7N25.23.02.21的毛坯类型是什么？
- 7N25.23.02.21的TYPE_ROUGH是什么？

### 2.8 叶根相关问题

**叶根图号 (ROOT)**
- 7N25.23.02.21的叶根图号是什么？
- 7N25.23.02.21的ROOT是什么？

**叶根宽度 (ROOT_WIDTH)**
- 7N25.23.02.21的叶根宽度是多少？
- 7N25.23.02.21的ROOT_WIDTH是多少？

**叶根宽度 (V01R)**
- 7N25.23.02.21的叶根宽度是多少？
- 7N25.23.02.21的V01R是多少？

### 2.9 其他技术参数问题

**型线数据文件名 (PROFILE)**
- 7N25.23.02.21的型线数据文件名是什么？
- 7N25.23.02.21的PROFILE是什么？

**叶型最大突出量 (UEBAUS)**
- 7N25.23.02.21的叶型最大突出量是多少？
- 7N25.23.02.21的UEBAUS是多少？

**压力面最小曲率半径 (KRURA)**
- 7N25.23.02.21的压力面最小曲率半径是多少？
- 7N25.23.02.21的KRURA是多少？

## 第三部分：可视化映射

### 3.1 去重数据与生成问题的映射关系

```mermaid
mindmap
  root((去重数据与问题映射))
    基础信息
      零件名称(PART_NAME)
        零件名称是什么？
        PART_NAME是什么？
        叫什么名字？
      零件图号(PART_NUMBER)
        零件图号是多少？
        PART_NUMBER是什么？
        图号是7N25.23.02.21吗？
      叶片类型(BLD_TYPE)
        是动叶还是静叶？
        叶片类型是什么？
        BLD_TYPE是什么？
      叶片级号(STAGE)
        叶片级号是多少？
        STAGE是什么？
        是第几级叶片？
      旋向(ROT)
        是左旋还是右旋？
        旋向是什么？
        ROT是多少？
    数量信息
      整级叶片只数(BLADES)
        整级叶片有多少只？
        BLADES是多少？
        总共有几片叶片？
      标准叶片只数(BLADES0)
        标准叶片有多少只？
        BLADES0是多少？
        标准叶片数量？
      加厚叶片只数(BLADES1)
        加厚叶片有多少只？
        BLADES1是多少？
        加厚叶片数量？
      末叶片只数(BLADES2)
        末叶片有多少只？
        BLADES2是多少？
        末叶片数量？
    尺寸信息
      外径(DA)
        外径是多少？
        DA是多少？
        外径尺寸？
      内径(DI)
        内径是多少？
        DI是多少？
        内径尺寸？
      其他直径参数
        DB、DA1、DI3等
        各种截面直径
        特定位置直径
    角度信息
      围带节距角(DECKWI)
        围带节距角是多少？
        DECKWI是多少？
      叶根节距角(FUSSWI)
        叶根节距角是多少？
        FUSSWI是多少？
      斜切角度
        WINKD1、WINKF1、WINKF2
        各种斜切角度参数
    重量信息
      重量(weight)
        重量是多少？
        有多重？
        weight是多少？
      净重量(NET_WEIGHT)
        净重量是多少？
        NET_WEIGHT是多少？
      毛坯质量(weight_rough)
        毛坯质量是多少？
        weight_rough是多少？
      末叶片重量(weight_fb)
        末叶片重量是多少？
        weight_fb是多少？
    材料信息
      材料名称(MATERIAL)
        用什么材料制作？
        材料名称是什么？
        MATERIAL是什么？
      热处理(HEAT_TREATMENT)
        热处理工艺是什么？
        HEAT_TREATMENT是什么？
      采购规范(MATERIAL_REF)
        叶片采购规范是什么？
        MATERIAL_REF是什么？
    类型信息
      叶根类型(TYPE)
        叶根类型是什么？
        TYPE是什么？
        采用什么类型叶根？
      毛坯类型(TYPE_ROUGH)
        毛坯类型是什么？
        TYPE_ROUGH是什么？
    叶根信息
      叶根图号(ROOT)
        叶根图号是什么？
        ROOT是什么？
      叶根宽度(ROOT_WIDTH)
        叶根宽度是多少？
        ROOT_WIDTH是多少？
      叶根宽度(V01R)
        叶根宽度是多少？
        V01R是多少？
    技术参数
      型线数据(PROFILE)
        型线数据文件名？
        PROFILE是什么？
      突出量(UEBAUS)
        叶型最大突出量？
        UEBAUS是多少？
      曲率半径(KRURA)
        压力面最小曲率半径？
        KRURA是多少？
```

### 3.2 原始BLADE_DATA_FIELD_NAME字段与问题的对应关系

```mermaid
mindmap
  root((原始数据字段映射))
    common层级
      基础字段
        PART_NAME → 零件名称问题
        PART_NUMBER → 零件图号问题
        BLD_TYPE → 叶片类型问题
        STAGE → 叶片级号问题
        ROT → 旋向问题
      数量字段
        BLADES → 整级叶片只数问题
        BLADES0 → 标准叶片只数问题
        BLADES1 → 加厚叶片只数问题
      尺寸字段
        DA → 外径问题
        DI → 内径问题
        DB → 最底部插值截面直径问题
        DA1 → A1截面直径问题
        DI3 → A2截面直径问题
      角度字段
        DECKWI → 围带节距角问题
        FUSSWI → 叶根节距角问题
        WINKD1 → 围带斜切角度问题
        WINKF1 → 叶根斜切角度问题
        WINKF2 → 叶根吸力面菱形角问题
      重量字段
        weight → 重量问题
        NET_WEIGHT → 净重量问题
        weight_rough → 毛坯质量问题
      材料字段
        MATERIAL → 材料名称问题
        HEAT_TREATMENT → 热处理问题
        MATERIAL_REF → 采购规范问题
      类型字段
        TYPE → 叶根类型问题
        TYPE_ROUGH → 毛坯类型问题
      其他字段
        PROFILE → 型线数据问题
        ROOT → 叶根图号问题
        UEBAUS → 叶型最大突出量问题
        KRURA → 压力面最小曲率半径问题
    rotor_1层级
      特有字段
        BLADES2 → 末叶片只数问题
        weight_fb → 末叶片重量问题
        V01R → 叶根宽度问题
        V02R → 叶根颈部宽度问题
        ROOT_WIDTH → 叶根宽度问题
      其他V系列参数
        V03R到V20R → 各种叶根几何参数问题
        V51R、V52R → 填隙条相关参数问题
    rotor_2层级
      重复字段
        BLADES2 → 末叶片只数问题(重复)
        weight_fb → 末叶片重量问题(重复)
    stator_3层级
      特有字段
        BLADES2 → 中分面叶片只数问题
        weight_fb → 中分面叶片重量问题
        V01R到V17R → 静叶叶根参数问题
        ROOT_WIDTH → 叶根宽度问题(重复)
    stator_4层级
      特有字段
        BLADES2 → 中分面叶片只数问题(重复)
        weight_fb → 中分面叶片重量问题(重复)
        V01R到V14R → 静叶叶根参数问题
        ROOT_WIDTH → 叶根宽度问题(重复)
```

### 3.3 完整性检查映射

```mermaid
flowchart TD
    A[原始BLADE_DATA_FIELD_NAME数据] --> B[数据去重处理]
    B --> C[去重后的属性列表]
    C --> D[问题生成]
    D --> E[生成的问题列表]

    F[验证流程] --> G{检查字段覆盖}
    G -->|完整| H[✓ 所有字段都有对应问题]
    G -->|不完整| I[✗ 存在遗漏字段]

    J[质量检查] --> K{检查问题质量}
    K -->|合格| L[✓ 问题表达清晰准确]
    K -->|不合格| M[✗ 问题需要优化]

    N[重复检查] --> O{检查重复问题}
    O -->|无重复| P[✓ 问题唯一性良好]
    O -->|有重复| Q[✗ 存在重复问题]

    style H fill:#90EE90
    style L fill:#90EE90
    style P fill:#90EE90
    style I fill:#FFB6C1
    style M fill:#FFB6C1
    style Q fill:#FFB6C1
```

## 第四部分：完整性验证和总结

### 4.1 数据处理统计

**原始数据统计：**
- common层级：139个字段
- rotor_1层级：26个字段
- rotor_2层级：2个字段
- stator_3层级：20个字段
- stator_4层级：18个字段
- **总计：205个字段**

**去重后统计：**
- 去重后唯一字段：**69个**
- 移除重复字段：**136个**
- 去重率：**66.3%**

### 4.2 问题生成覆盖率

**问题生成统计：**
- 基础信息类：5个属性，15个问题
- 数量相关类：4个属性，12个问题
- 尺寸相关类：5个属性，15个问题
- 角度相关类：5个属性，10个问题
- 重量相关类：4个属性，12个问题
- 材料相关类：3个属性，9个问题
- 类型相关类：2个属性，6个问题
- 叶根相关类：3个属性，9个问题
- 其他技术参数：3个属性，9个问题

**总计：34个属性，97个问题**

**注意：** 还有35个属性（主要是V系列参数、截面参数等）需要补充问题生成。

### 4.3 使用建议

1. **JavaScript代码使用：**
   - 将去重方法集成到数据处理流程中
   - 使用验证方法确保数据完整性
   - 定期运行验证检查数据一致性

2. **问题扩展：**
   - 可以根据实际需求增加更多问法
   - 可以添加组合查询问题
   - 可以根据用户反馈优化问题表达

3. **维护更新：**
   - 当BLADE_DATA_FIELD_NAME数据结构变化时，重新运行去重处理
   - 定期检查问题的准确性和完整性
   - 根据实际使用情况调整问题优先级

### 4.4 扩展功能建议

1. **智能问题匹配：**
   - 可以开发问题相似度匹配算法
   - 支持模糊查询和智能推荐
   - 提供问题分类和标签功能

2. **数据验证增强：**
   - 添加数据类型验证
   - 支持数值范围检查
   - 提供数据完整性报告

3. **可视化增强：**
   - 可以生成交互式的数据关系图
   - 支持动态筛选和搜索
   - 提供数据统计和分析图表

---

**文档生成完成时间：** 2025-06-17
**数据版本：** BLADE_DATA_FIELD_NAME v1.0
**处理状态：** ✅ 完成去重处理、问题生成、可视化映射
